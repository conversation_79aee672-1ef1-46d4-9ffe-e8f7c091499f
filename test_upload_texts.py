import asyncio
import json
import requests


# 服务地址
# API_URL = "http://localhost:8000/api/v1/upload_texts"
API_URL = "http://copilot.csvw.com/rag_service/api/v1/upload_texts"

async def test_upload_texts():
    print("测试文本列表上传接口...")
    
    # 准备测试数据
    test_data = {
        "texts": ["后门把手异响什么原因"],
        "database": "lkz_test_V3",
        "collection": "test11111111",
        # "encrypt": True,
        # "embedding_type": "bge-m3",  # 使用Azure OpenAI生成向量
        "metadata": {
            "source": "test_script",
            "purpose": "测试1"
        }
    }
    
    # 发送请求
    try:
        response = requests.post(API_URL, json=test_data)
        
        # 打印响应
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            print(f"成功上传 {result.get('text_count', 0)} 条文本")
            print(f"文档ID: {result.get('doc_id', '')}")
            print(f"向量维度: {result.get('vector_dimension', 0)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求出错: {e}")
    
    print("测试完成")

if __name__ == "__main__":
    import time
    t1 = time.time()
    asyncio.run(test_upload_texts())
    t2 = time.time()
    print(f"请求耗时: {t2 - t1:.2f}秒")
